

- fix: 自动化-创建记录action选中的关联项需要显示在关联项列表
- fix: Super agent-打开公告窗口后，Chief of Staff描述tips未消失
- feat: FIleArtifact集成KKFilePreview(wip)	- feat: FIleArtifact集成KKFilePreview
- fix: 文件夹页面节点icon没有自动更新
- fix: 通过资源编辑器修改节点icon，用AI生成icon头像时报"onApply callback must be provided"错误 #15874
refactor: organization chart get skillsets use searchTalks api 
ui: Flow Editor with Dashboard widget #16051
	fix: 成员字段搜索时屏蔽AI Agent结果 #16300

feat: Skillsets Selector 选择技能后的交互优化
fix: generate_slides 的 Artifact 里面的问题合集
fix: AI agent-generate_slides 执行后显示network error	各类交互和UI优化
- feat: space home expert skillset icon
- fix: sidebar_team, AI agent卡片中更换头像后，更换后的头像不会在卡片中展示出来 #16114
- fix: Space Launcher，agent card 里面技能icon的大小调整为20px #16205
- feat: 架构图成员悬浮显示技能集
	ui: Flow Editor with Dashboard widget #16051

- fix: AI agent, companies 没有加载完成时，Artifact会显示"No data to display"，缺少动态加载效果 #16247
- fix: AI agent, Companies tool生成过程中，artifact没有显示出来 #16172
- fix: AI agent-万能 Artifact需要支持复制和展开 	- fix: onboarding 模板卡片屏蔽跳转入口
- fix: Super agent-打开公告窗口后，Chief of Staff描述tips未消失

feat: adjust video and generate slides styles #16248
feat: added ai skillset icon in input text box	feat: added ai skillset icon in input text box
- feat: space home expert skillset icon
- fix: sidebar_team, AI agent卡片中更换头像后，更换后的头像不会在卡片中展示出来 #16114
- fix: Space Launcher，agent card 里面技能icon的大小调整为20px #16205	- feat: 架构图成员悬浮显示技能集
- message expert icon
- fix: Artifact对话框不能停止问题
- ui: Refine Installer Installer's styling
- ui: Refine styling for DefaultArtifact Other artifact	- fix: onboarding 选择角色安装完所有模板后，Resource未出现安装的模板
- create_document artifact 展示issue (partof 15595)

- fix: AI agent，bika_search_images tool生成过程中，artifact不会自动显示出来
- fix: AI agent, Companies tool生成过程中，artifact没有显示出来
- refact: DefaultArtifact 添加展开按钮	- fix: onboarding 选择角色安装完所有模板后，Resource未出现安装的模板
- fix: Artifact对话框不能停止问题
- fix: agent builder,agent installer tool中图标icon没有显示

请假	- ui: Flow Editor with Dashboard widget #16051
- feat: 架构图成员悬浮显示技能集

- fix：页面崩溃
- feat：新的视频弹窗	处理 Issue
上周
- image to text artifact	本周
- 处理 Issue
上周
feat: ui: slides artifact
feat: SkillSelect，往下滚动，可以点击「加载更多」
ui: adjust some ui (#16082)
ui: 替换模板中心顶部为全新的的SVG
ui: 调整官网的模板卡片组件 支持设置列数量
fix: skillsets approval switch button style is incorrect (#16089)
fix: favicon.ico	增加 agent 编辑权限功能 和 agent 技能显示
[上周]
- feat: Templates Onboarding
- fix: tool UI 错误状态支持
- searchPage Artifact 完善
- fix: 表单中已选择的关联记录无法取消


[小组上周]
🎯 主要新功能开发
Slides Artifact: UI界面开发和样式调整
审批流程开发
NodeCard中skillsets图标添加
Space home expert skillset图标
Image to Text Artifact: 新功能开发
Templates Onboarding: 模板引导功能


🔧 重要修复
组织架构问题: 根小组头像显示错误，应显示空间站头像
自动化变量: 变量保存不生效问题修复
Chat数据混乱: 修复A空间站Chat数据混入B空间站的问题
表单关联记录: 已选择的关联记录无法取消的问题
文档分享权限: 非空间站成员编辑权限异常修复


	[本周]
- AI Artifact 场景实现(Company等)
- Call to Chat Action
- 已有问题处理

小周本周
chenwei: 增加 agent 编辑权限功能 和 agent 技能显示
组织架构图处理
Artifact 功能完善
fix: Installer 样式 
fix: SearchPage 样式

fix: AI agent, bika_search_images tool 生成过程中点击"停止按钮"，tool不会停止会继续生成